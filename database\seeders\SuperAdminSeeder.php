<?php

namespace Database\Seeders;

use App\Models\DocumentCode;
use App\Models\OperatingUnit;
use App\Models\Role;
use App\Models\User;
use App\Models\UserPreference;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class SuperAdminSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // First, create the Central Administration operating unit
        $centralAdmin = OperatingUnit::create([
            'name' => 'Central Administration',
            'code' => 'CENTRAL',
            'email' => '<EMAIL>',
        ]);

        // Get the super_admin role
        $superAdminRole = Role::where('slug', 'super_admin')->first();

        if (!$superAdminRole) {
            $this->command->error('Super Admin role not found. Please run the RoleSeeder first.');
            return;
        }

        // Create the super admin user
        User::create([
            'name' => 'DMMMSU - CA Records',
            'email' => '<EMAIL>',
            'password' => Hash::make('Ilovedmmmsu_01'),
            'operating_unit_id' => $centralAdmin->id,
            'role_id' => $superAdminRole->id,
        ]);

        UserPreference::create([
            'user_id' => 1, // Assuming the super admin user is the first user created
            'preference_key' => 'document_column_arrangement',
            'preference_value' => json_encode([
                'control_number',
                'reference_document',
                'signatory',
                'received_by.name',
                'origin',
                'document_code',
                'subject_matter',
                'referred_to',
                'date_received',
                'time_received',
                'status'
            ]),
        ]);

       DocumentCode::create([
                'operating_unit_id' => 1, // Assuming the super admin user is the first user created
                'operating_unit_code_key' => 'document_code_key',
                'operating_unit_code_value' => json_encode(['N/A']),
            ]);

        $this->command->info('Super Admin user created successfully.');
    }
}
