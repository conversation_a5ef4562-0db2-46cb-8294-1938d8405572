<?php

namespace App\Http\Controllers;

use App\Models\ArchiveFolder;
use App\Models\ArchiveFile;
use App\Models\OperatingUnit;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;
use Inertia\Inertia;

class ArchiveController extends Controller
{
    /**
     * Display the main archive page with folders only (no files at root level).
     */
    public function index()
    {
        $user = Auth::user()->load('operatingUnit');

        // Check if user has operating unit
        if (!$user->operating_unit_id) {
            abort(403, 'User must be assigned to an operating unit to access archive.');
        }

        // Get all folders for the current user's operating unit
        $folders = ArchiveFolder::forOperatingUnit($user->operating_unit_id)
            ->orderBy('name')
            ->get();

        return Inertia::render('Archive/Index', [
            'folders' => $folders,
            'operatingUnit' => $user->operatingUnit,
        ]);
    }

    /**
     * Display the contents of a specific folder.
     */
    public function showFolder($id)
    {
        $user = Auth::user();

        // Get the folder with access control
        $folder = ArchiveFolder::forOperatingUnit($user->operating_unit_id)
            ->findOrFail($id);

        // Get files in this specific folder
        $files = ArchiveFile::inFolder($id)
            ->with(['uploader'])
            ->orderBy('original_name')
            ->get();

        // Get all folders for move operations (excluding current folder)
        $allFolders = ArchiveFolder::forOperatingUnit($user->operating_unit_id)
            ->where('id', '!=', $id)
            ->select('id', 'name', 'file_count')
            ->orderBy('name')
            ->get();

        return Inertia::render('Archive/Folder', [
            'folder' => $folder,
            'files' => $files,
            'operatingUnit' => $user->operatingUnit,
            'availableFolders' => $allFolders,
        ]);
    }

    /**
     * Show a specific archive file.
     */
    public function show($id)
    {
        $user = Auth::user();

        // Get the archive file with access control
        $archiveFile = ArchiveFile::forOperatingUnit($user->operating_unit_id)
            ->with(['folder', 'uploader'])
            ->findOrFail($id);

        // Read the file content based on type
        $fileContent = $this->getFileContent($archiveFile);
        $fileType = $this->getFileType($archiveFile->original_name);

        // For Excel files, provide the file path for client-side parsing
        $fileUrl = null;
        if (in_array($fileType, ['xlsx', 'xls']) && $archiveFile->exists()) {
            $fileUrl = '/storage/' . $archiveFile->file_path;
        }

        // Get all folders for move operations (excluding current folder)
        $allFolders = ArchiveFolder::forOperatingUnit($user->operating_unit_id)
            ->where('id', '!=', $archiveFile->folder_id)
            ->select('id', 'name', 'file_count')
            ->orderBy('name')
            ->get();

        return Inertia::render('Archive/Show', [
            'archiveFile' => $archiveFile,
            'fileContent' => $fileContent,
            'fileType' => $fileType,
            'fileUrl' => $fileUrl,
            'availableFolders' => $allFolders,
        ]);
    }

    /**
     * Create a new folder.
     */
    public function createFolder(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
        ]);

        $user = Auth::user();

        // Check if user has operating unit
        if (!$user->operating_unit_id) {
            return back()->withErrors([
                'error' => 'User must be assigned to an operating unit to create folders.'
            ]);
        }

        // Check for duplicate folder name in the same operating unit
        $existingFolder = ArchiveFolder::where('operating_unit_id', $user->operating_unit_id)
            ->where('name', $request->name)
            ->first();

        if ($existingFolder) {
            return back()->withErrors([
                'name' => 'A folder with this name already exists.'
            ]);
        }

        try {
            $folder = ArchiveFolder::create([
                'name' => $request->name,
                'description' => $request->description,
                'operating_unit_id' => $user->operating_unit_id,
                'created_by' => $user->id,
            ]);

            return back()->with('success', 'Folder created successfully');
        } catch (\Exception $e) {
            return back()->withErrors([
                'error' => 'Failed to create folder: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Upload Excel files (XLSX, CSV) to a specific folder.
     */
    public function uploadFiles(Request $request, $folderId)
    {
        // Custom validation for Excel and CSV files
        $request->validate([
            'files' => 'required|array',
            'files.*' => [
                'required',
                'file',
                'max:51200', // 50MB max
                function ($attribute, $value, $fail) {
                    $allowedExtensions = ['xlsx', 'xls', 'csv'];
                    $allowedMimeTypes = [
                        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // .xlsx
                        'application/vnd.ms-excel', // .xls
                        'text/csv', // .csv
                        'application/csv', // .csv (alternative)
                        'text/plain', // .csv (sometimes detected as plain text)
                        'application/vnd.ms-excel', // .csv (sometimes detected as Excel)
                    ];

                    $extension = strtolower($value->getClientOriginalExtension());
                    $mimeType = $value->getMimeType();

                    if (!in_array($extension, $allowedExtensions) && !in_array($mimeType, $allowedMimeTypes)) {
                        $fail('The ' . $attribute . ' field must be a file of type: xlsx, xls, csv.');
                    }
                }
            ]
        ]);

        $user = Auth::user();

        // Get the folder with access control
        $folder = ArchiveFolder::forOperatingUnit($user->operating_unit_id)
            ->findOrFail($folderId);

        $uploadedFiles = [];
        $duplicates = [];

        try {
            if ($request->hasFile('files')) {
                foreach ($request->file('files') as $file) {
                    $originalName = $file->getClientOriginalName();

                    // Debug logging for file types
                    \Log::info('File upload debug', [
                        'original_name' => $originalName,
                        'mime_type' => $file->getMimeType(),
                        'extension' => $file->getClientOriginalExtension(),
                        'size' => $file->getSize()
                    ]);

                    // Check for duplicate files in the same folder
                    $existingFile = ArchiveFile::where('folder_id', $folderId)
                        ->where('original_name', $originalName)
                        ->first();

                    if ($existingFile) {
                        // File with same name exists, add to duplicates array
                        $duplicates[] = [
                            'original_name' => $originalName,
                            'existing_file_id' => $existingFile->id,
                            'file_data' => base64_encode(file_get_contents($file->getPathname())),
                            'mime_type' => $file->getMimeType(),
                            'size' => $file->getSize(),
                            'extension' => $file->getClientOriginalExtension(),
                        ];
                        continue;
                    }

                    // No duplicate, proceed with upload
                    $storedName = Str::uuid() . '.' . $file->getClientOriginalExtension();

                    // Store the file
                    $path = $file->storeAs(
                        "archive/{$user->operating_unit_id}/folder_{$folderId}",
                        $storedName,
                        'public'
                    );

                    // Create file record in database
                    $archiveFile = ArchiveFile::create([
                        'original_name' => $originalName,
                        'stored_name' => $storedName,
                        'file_path' => $path,
                        'mime_type' => $file->getMimeType(),
                        'size' => $file->getSize(),
                        'extension' => $file->getClientOriginalExtension(),
                        'folder_id' => $folderId,
                        'operating_unit_id' => $user->operating_unit_id,
                        'uploaded_by' => $user->id,
                    ]);

                    $uploadedFiles[] = $archiveFile->load('uploader');
                }
            }

            // Update folder file count
            $folder->updateFileCount();

            // If there are duplicates, return them for user decision
            if (!empty($duplicates)) {
                return response()->json([
                    'duplicates_found' => true,
                    'duplicates' => $duplicates,
                    'uploaded_files' => $uploadedFiles,
                    'message' => 'Some files have the same name as existing files in this folder.',
                ]);
            }

            return response()->json([
                'success' => true,
                'message' => 'Files uploaded successfully',
                'files' => $uploadedFiles,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Failed to upload files: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Handle duplicate file resolution.
     */
    public function resolveDuplicates(Request $request, $folderId)
    {
        $request->validate([
            'duplicates' => 'required|array',
            'duplicates.*.action' => 'required|in:proceed,overwrite',
            'duplicates.*.original_name' => 'required|string',
            'duplicates.*.file_data' => 'required|string',
            'duplicates.*.mime_type' => 'required|string',
            'duplicates.*.size' => 'required|integer',
            'duplicates.*.extension' => 'required|string',
            'duplicates.*.existing_file_id' => 'required_if:duplicates.*.action,overwrite|integer',
        ]);

        $user = Auth::user();

        // Get the folder with access control
        $folder = ArchiveFolder::forOperatingUnit($user->operating_unit_id)
            ->findOrFail($folderId);

        $uploadedFiles = [];
        $overwrittenFiles = [];

        try {
            foreach ($request->duplicates as $duplicate) {
                $originalName = $duplicate['original_name'];
                $fileData = base64_decode($duplicate['file_data']);

                // Create temporary file from base64 data
                $tempFile = tmpfile();
                fwrite($tempFile, $fileData);
                $tempPath = stream_get_meta_data($tempFile)['uri'];

                if ($duplicate['action'] === 'overwrite') {
                    // Overwrite existing file
                    $existingFile = ArchiveFile::findOrFail($duplicate['existing_file_id']);

                    // Delete old physical file
                    $existingFile->deleteFile();

                    // Generate new stored name
                    $storedName = Str::uuid() . '.' . $duplicate['extension'];

                    // Store the new file
                    $path = Storage::disk('public')->putFileAs(
                        "archive/{$user->operating_unit_id}/folder_{$folderId}",
                        new \Illuminate\Http\File($tempPath),
                        $storedName
                    );

                    // Update existing file record
                    $existingFile->update([
                        'stored_name' => $storedName,
                        'file_path' => $path,
                        'mime_type' => $duplicate['mime_type'],
                        'size' => $duplicate['size'],
                        'uploaded_by' => $user->id,
                        'updated_at' => now(),
                    ]);

                    $overwrittenFiles[] = $existingFile->fresh()->load('uploader');

                } else {
                    // Proceed with numbered duplicate
                    $numberedName = $this->generateNumberedFileName($originalName, $folderId);
                    $storedName = Str::uuid() . '.' . $duplicate['extension'];

                    // Store the file
                    $path = Storage::disk('public')->putFileAs(
                        "archive/{$user->operating_unit_id}/folder_{$folderId}",
                        new \Illuminate\Http\File($tempPath),
                        $storedName
                    );

                    // Create new file record with numbered name
                    $archiveFile = ArchiveFile::create([
                        'original_name' => $numberedName,
                        'stored_name' => $storedName,
                        'file_path' => $path,
                        'mime_type' => $duplicate['mime_type'],
                        'size' => $duplicate['size'],
                        'extension' => $duplicate['extension'],
                        'folder_id' => $folderId,
                        'operating_unit_id' => $user->operating_unit_id,
                        'uploaded_by' => $user->id,
                    ]);

                    $uploadedFiles[] = $archiveFile->load('uploader');
                }

                // Close temporary file
                fclose($tempFile);
            }

            // Update folder file count
            $folder->updateFileCount();

            return response()->json([
                'success' => true,
                'message' => 'Duplicate files resolved successfully',
                'uploaded_files' => $uploadedFiles,
                'overwritten_files' => $overwrittenFiles,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Failed to resolve duplicates: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Generate a numbered filename for duplicates.
     */
    private function generateNumberedFileName($originalName, $folderId)
    {
        $pathInfo = pathinfo($originalName);
        $baseName = $pathInfo['filename'];
        $extension = isset($pathInfo['extension']) ? '.' . $pathInfo['extension'] : '';

        $counter = 1;
        do {
            $numberedName = $baseName . " ({$counter})" . $extension;
            $exists = ArchiveFile::where('folder_id', $folderId)
                ->where('original_name', $numberedName)
                ->exists();
            $counter++;
        } while ($exists);

        return $numberedName;
    }

    /**
     * Move a file to a different folder.
     */
    public function moveFile(Request $request, $id)
    {
        $request->validate([
            'destination_folder_id' => 'required|integer|exists:archive_folders,id',
        ]);

        $user = Auth::user();

        // Get the archive file with access control
        $archiveFile = ArchiveFile::forOperatingUnit($user->operating_unit_id)
            ->with(['folder'])
            ->findOrFail($id);

        // Get the destination folder with access control
        $destinationFolder = ArchiveFolder::forOperatingUnit($user->operating_unit_id)
            ->findOrFail($request->destination_folder_id);

        // Check if the file is already in the destination folder
        if ($archiveFile->folder_id == $destinationFolder->id) {
            return response()->json([
                'error' => 'File is already in the selected folder.'
            ], 400);
        }

        try {
            // Store the original folder for updating file counts
            $originalFolder = $archiveFile->folder;

            // Update the file's folder
            $archiveFile->folder_id = $destinationFolder->id;
            $archiveFile->save();

            // Update file counts for both folders
            if ($originalFolder) {
                $originalFolder->updateFileCount();
            }
            $destinationFolder->updateFileCount();

            return response()->json([
                'success' => true,
                'message' => "File moved to '{$destinationFolder->name}' successfully.",
                'original_folder' => $originalFolder ? $originalFolder->name : 'Unknown',
                'destination_folder' => $destinationFolder->name,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Failed to move file: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Bulk move files to a different folder.
     */
    public function bulkMoveFiles(Request $request)
    {
        $request->validate([
            'file_ids' => 'required|array|min:1',
            'file_ids.*' => 'integer|exists:archive_files,id',
            'destination_folder_id' => 'required|integer|exists:archive_folders,id',
        ]);

        $user = Auth::user();

        // Get all files with access control
        $archiveFiles = ArchiveFile::forOperatingUnit($user->operating_unit_id)
            ->with(['folder'])
            ->whereIn('id', $request->file_ids)
            ->get();

        if ($archiveFiles->count() !== count($request->file_ids)) {
            return response()->json([
                'error' => 'Some files were not found or you do not have permission to access them.'
            ], 400);
        }

        // Get the destination folder with access control
        $destinationFolder = ArchiveFolder::forOperatingUnit($user->operating_unit_id)
            ->findOrFail($request->destination_folder_id);

        try {
            $movedCount = 0;
            $skippedCount = 0;
            $affectedFolders = collect();

            foreach ($archiveFiles as $archiveFile) {
                // Skip if file is already in the destination folder
                if ($archiveFile->folder_id == $destinationFolder->id) {
                    $skippedCount++;
                    continue;
                }

                // Store the original folder for updating file counts
                $originalFolder = $archiveFile->folder;
                if ($originalFolder) {
                    $affectedFolders->push($originalFolder);
                }

                // Update the file's folder
                $archiveFile->folder_id = $destinationFolder->id;
                $archiveFile->save();
                $movedCount++;
            }

            // Update file counts for all affected folders
            $affectedFolders->push($destinationFolder);
            $affectedFolders->unique('id')->each(function ($folder) {
                $folder->updateFileCount();
            });

            $message = "Successfully moved {$movedCount} file(s) to '{$destinationFolder->name}'.";
            if ($skippedCount > 0) {
                $message .= " {$skippedCount} file(s) were already in the destination folder.";
            }

            return response()->json([
                'success' => true,
                'message' => $message,
                'moved_count' => $movedCount,
                'skipped_count' => $skippedCount,
                'destination_folder' => $destinationFolder->name,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Failed to move files: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Bulk delete files.
     */
    public function bulkDeleteFiles(Request $request)
    {
        $request->validate([
            'file_ids' => 'required|array|min:1',
            'file_ids.*' => 'integer|exists:archive_files,id',
        ]);

        $user = Auth::user();

        // Get all files with access control
        $archiveFiles = ArchiveFile::forOperatingUnit($user->operating_unit_id)
            ->with(['folder'])
            ->whereIn('id', $request->file_ids)
            ->get();

        if ($archiveFiles->count() !== count($request->file_ids)) {
            return response()->json([
                'error' => 'Some files were not found or you do not have permission to access them.'
            ], 400);
        }

        try {
            $deletedCount = 0;
            $affectedFolders = collect();

            foreach ($archiveFiles as $archiveFile) {
                // Store the folder for updating file counts
                if ($archiveFile->folder) {
                    $affectedFolders->push($archiveFile->folder);
                }

                // Delete the physical file
                $archiveFile->deleteFile();

                // Delete the database record
                $archiveFile->delete();
                $deletedCount++;
            }

            // Update file counts for all affected folders
            $affectedFolders->unique('id')->each(function ($folder) {
                $folder->updateFileCount();
            });

            return response()->json([
                'success' => true,
                'message' => "Successfully deleted {$deletedCount} file(s).",
                'deleted_count' => $deletedCount,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Failed to delete files: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Rename an archive file.
     */
    public function updateFile(Request $request, $id)
    {
        $request->validate([
            'name' => 'required|string|max:255',
        ]);

        $user = Auth::user();

        // Get the archive file with access control
        $archiveFile = ArchiveFile::forOperatingUnit($user->operating_unit_id)
            ->findOrFail($id);

        try {
            $archiveFile->update([
                'original_name' => $request->name,
            ]);

            return response()->json([
                'success' => true,
                'message' => 'File renamed successfully',
                'file' => $archiveFile->fresh(),
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Failed to rename file: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Delete an archive file.
     */
    public function destroy($id)
    {
        $user = Auth::user();

        // Get the archive file with access control
        $archiveFile = ArchiveFile::forOperatingUnit($user->operating_unit_id)
            ->findOrFail($id);

        try {
            // Delete the physical file
            $archiveFile->deleteFile();

            // Get folder before deleting the file record
            $folder = $archiveFile->folder;

            // Delete the database record
            $archiveFile->delete();

            // Update folder file count
            $folder->updateFileCount();

            return response()->json([
                'success' => true,
                'message' => 'File deleted successfully',
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Failed to delete file: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Rename a folder.
     */
    public function renameFolder(Request $request, $id)
    {
        $request->validate([
            'name' => 'required|string|max:255',
        ]);

        $user = Auth::user();

        // Get the folder with access control
        $folder = ArchiveFolder::forOperatingUnit($user->operating_unit_id)
            ->findOrFail($id);

        // Check if the new name is the same as the current name
        if ($request->name === $folder->name) {
            return response()->json([
                'success' => true,
                'message' => 'No changes made',
                'folder' => $folder,
            ]);
        }

        // Check for duplicate folder name in the same operating unit (excluding current folder)
        $existingFolder = ArchiveFolder::where('operating_unit_id', $user->operating_unit_id)
            ->where('name', $request->name)
            ->where('id', '!=', $id)
            ->first();

        if ($existingFolder) {
            return response()->json([
                'error' => 'A folder with this name already exists.'
            ], 422);
        }

        try {
            $folder->update([
                'name' => $request->name,
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Folder renamed successfully',
                'folder' => $folder->fresh(),
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Failed to rename folder: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get folder details including files for deletion confirmation.
     */
    public function getFolderDetails($id)
    {
        $user = Auth::user();

        // Get the folder with access control
        $folder = ArchiveFolder::forOperatingUnit($user->operating_unit_id)
            ->findOrFail($id);

        // Get all files in the folder
        $files = $folder->files()
            ->select('id', 'original_name', 'size', 'created_at')
            ->orderBy('original_name')
            ->get();

        return response()->json([
            'folder' => [
                'id' => $folder->id,
                'name' => $folder->name,
                'file_count' => $files->count(),
            ],
            'files' => $files->map(function ($file) {
                return [
                    'id' => $file->id,
                    'name' => $file->original_name,
                    'size' => $file->size,
                    'created_at' => $file->created_at->format('Y-m-d H:i:s'),
                ];
            }),
        ]);
    }

    /**
     * Delete a folder and all its files.
     */
    public function deleteFolder($id)
    {
        $user = Auth::user();

        // Get the folder with access control
        $folder = ArchiveFolder::forOperatingUnit($user->operating_unit_id)
            ->findOrFail($id);

        try {
            // Get all files in the folder
            $files = $folder->files;

            // Delete all files first
            foreach ($files as $file) {
                // Delete the physical file
                $file->deleteFile();
                // Delete the database record
                $file->delete();
            }

            // Delete the folder
            $folder->delete();

            return response()->json([
                'success' => true,
                'message' => 'Folder and all files deleted successfully',
                'deleted_files_count' => $files->count(),
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Failed to delete folder: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Bulk delete folders and all their files.
     */
    public function bulkDeleteFolders(Request $request)
    {
        $request->validate([
            'folder_ids' => 'required|array|min:1',
            'folder_ids.*' => 'integer|exists:archive_folders,id',
        ]);

        $user = Auth::user();

        // Get all folders with access control
        $folders = ArchiveFolder::forOperatingUnit($user->operating_unit_id)
            ->with(['files'])
            ->whereIn('id', $request->folder_ids)
            ->get();

        if ($folders->count() !== count($request->folder_ids)) {
            return response()->json([
                'error' => 'Some folders were not found or you do not have permission to access them.'
            ], 400);
        }

        try {
            $deletedFoldersCount = 0;
            $deletedFilesCount = 0;

            foreach ($folders as $folder) {
                // Get all files in the folder
                $files = $folder->files;

                // Delete all files first
                foreach ($files as $file) {
                    // Delete the physical file
                    $file->deleteFile();
                    // Delete the database record
                    $file->delete();
                    $deletedFilesCount++;
                }

                // Delete the folder
                $folder->delete();
                $deletedFoldersCount++;
            }

            return response()->json([
                'success' => true,
                'message' => "Successfully deleted {$deletedFoldersCount} folder(s) and {$deletedFilesCount} file(s).",
                'deleted_folders_count' => $deletedFoldersCount,
                'deleted_files_count' => $deletedFilesCount,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Failed to delete folders: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get file content based on file type.
     */
    private function getFileContent($archiveFile)
    {
        $fileType = $this->getFileType($archiveFile->original_name);

        if ($fileType === 'csv') {
            // For CSV files, try to read and return content
            if ($archiveFile->exists()) {
                try {
                    $content = Storage::disk('public')->get($archiveFile->file_path);
                    return $content;
                } catch (\Exception $e) {
                    return "Error reading CSV file: " . $e->getMessage();
                }
            }
            return "CSV file not found.";
        } else {
            // For XLSX files, return a message since we can't display binary content directly
            return "Excel file content preview is not available. Please download the file to view its contents.";
        }
    }

    /**
     * Get file type from filename.
     */
    private function getFileType($filename)
    {
        $extension = strtolower(pathinfo($filename, PATHINFO_EXTENSION));
        return $extension;
    }
}
