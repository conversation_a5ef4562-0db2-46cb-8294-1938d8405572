<template>
    <div>
        <!-- Loading State -->
        <v-card v-if="loading" class="pa-4">
            <v-card-text class="text-center">
                <v-progress-circular indeterminate color="primary" size="64"></v-progress-circular>
                <div class="mt-4 text-h6">Parsing Excel file...</div>
            </v-card-text>
        </v-card>

        <!-- Error State -->
        <v-card v-else-if="error" color="error" variant="outlined" class="pa-4">
            <v-card-text>
                <div class="d-flex align-center">
                    <v-icon icon="mdi-alert-circle" class="mr-2"></v-icon>
                    <div>
                        <div class="text-h6">Error parsing Excel file</div>
                        <div class="mt-1 text-body-2">{{ error }}</div>
                    </div>
                </div>
            </v-card-text>
        </v-card>

        <!-- Excel Data Table -->
        <v-card v-else-if="parsedData.length > 0" elevation="2">
            <v-card-title class="d-flex align-center pa-4" style="background-color: white;">
                <v-icon icon="mdi-file-excel" class="mr-2" color="green-darken-2"></v-icon>
                <span class="text-h6 font-weight-medium">{{ filename }}</span>
                <v-chip v-if="totalRows > 0" color="primary" size="small" class="ml-2">
                    {{ totalRows.toLocaleString() }} rows
                </v-chip>
                <v-spacer></v-spacer>
                <div style="width: 300px">
                    <v-text-field v-model="search" append-icon="mdi-magnify" label="Search in table..." single-line
                        hide-details variant="outlined" density="compact"></v-text-field>
                </div>
            </v-card-title>

            <!-- Horizontal scrollable container for the table -->
            <div class="table-container">
                <v-data-table :headers="headers" :items="parsedData" :search="search" :loading="loading"
                    class="elevation-1" density="compact" fixed-header hover
                    :items-per-page-options="itemsPerPageOptions" :items-per-page="itemsPerPage"
                    @update:options="handleTableOptions">
                    <!-- Custom styling for headers -->
                    <template v-slot:headers>
                        <tr>
                            <th v-for="header in headers" :key="header.key" class="v-data-table__th text-start"
                                :style="{ width: header.width || 'auto', minWidth: header.width || 'auto' }">
                                {{ header.title }}
                            </th>
                        </tr>
                    </template>

                    <!-- Handle empty state -->
                    <template v-slot:no-data>
                        <div class="text-center pa-4">
                            <v-icon size="64" color="grey-lighten-1">mdi-table-off</v-icon>
                            <div class="mt-2 text-h6 text-grey-darken-1">No data found</div>
                            <div class="text-body-2 text-grey-darken-1">
                                The Excel file appears to be empty or contains no readable data.
                            </div>
                        </div>
                    </template>
                </v-data-table>
            </div>
        </v-card>

        <!-- Empty State -->
        <v-card v-else class="pa-4">
            <v-card-text class="text-center">
                <v-icon size="64" color="grey-lighten-1">mdi-file-excel-outline</v-icon>
                <div class="mt-2 text-h6 text-grey-darken-1">No data to display</div>
                <div class="text-body-2 text-grey-darken-1">
                    The Excel file appears to be empty or contains no readable data.
                </div>
            </v-card-text>
        </v-card>

        <!-- Warning Dialog for Large Datasets -->
        <v-dialog v-model="showAllWarning" max-width="500" persistent>
            <v-card>
                <v-card-title class="text-h5 d-flex align-center">
                    <v-icon icon="mdi-alert" color="warning" class="mr-2"></v-icon>
                    Performance Warning
                </v-card-title>
                <v-card-text>
                    <p class="mb-3">
                        This Excel file contains <strong>{{ totalRows.toLocaleString() }} rows</strong>.
                        Displaying all rows at once may cause browser performance issues or slow loading.
                    </p>
                    <p class="mb-0">
                        Are you sure you want to display all {{ totalRows.toLocaleString() }} rows?
                    </p>
                </v-card-text>
                <v-card-actions>
                    <v-spacer></v-spacer>
                    <v-btn color="grey" variant="text" @click="cancelShowAll">
                        Cancel
                    </v-btn>
                    <v-btn color="warning" variant="flat" @click="confirmShowAll">
                        Show All Rows
                    </v-btn>
                </v-card-actions>
            </v-card>
        </v-dialog>
    </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import * as XLSX from 'xlsx';

const props = defineProps({
    fileUrl: {
        type: String,
        required: true
    },
    filename: {
        type: String,
        required: true
    }
});

// Reactive data
const loading = ref(true);
const error = ref(null);
const parsedData = ref([]);
const headers = ref([]);
const search = ref('');
const itemsPerPage = ref(10);
const itemsPerPageOptions = ref([]);
const showAllWarning = ref(false);
const totalRows = ref(0);

// Methods
const calculatePaginationOptions = (rowCount) => {
    totalRows.value = rowCount;

    if (rowCount <= 20) {
        // Small datasets: [5, 10, All]
        itemsPerPageOptions.value = [
            { title: '5', value: 5 },
            { title: '10', value: 10 },
            { title: 'All', value: -1 }
        ];
        itemsPerPage.value = Math.min(10, rowCount);
    } else if (rowCount <= 100) {
        // Medium datasets: [10, 20, 50, All]
        itemsPerPageOptions.value = [
            { title: '10', value: 10 },
            { title: '20', value: 20 },
            { title: '50', value: 50 },
            { title: 'All', value: -1 }
        ];
        itemsPerPage.value = 20;
    } else if (rowCount <= 500) {
        // Large datasets: [20, 50, 100, All]
        itemsPerPageOptions.value = [
            { title: '20', value: 20 },
            { title: '50', value: 50 },
            { title: '100', value: 100 },
            { title: 'All', value: -1 }
        ];
        itemsPerPage.value = 50;
    } else {
        // Very large datasets: [50, 100, 200, All]
        itemsPerPageOptions.value = [
            { title: '50', value: 50 },
            { title: '100', value: 100 },
            { title: '200', value: 200 },
            { title: 'All', value: -1 }
        ];
        itemsPerPage.value = 100;
    }
};

const handleShowAll = () => {
    if (totalRows.value > 1000) {
        showAllWarning.value = true;
    } else {
        itemsPerPage.value = -1;
    }
};

const confirmShowAll = () => {
    itemsPerPage.value = -1;
    showAllWarning.value = false;
};

const cancelShowAll = () => {
    showAllWarning.value = false;
};

// Helper function to check if a cell should be treated as a date
const isExcelDate = (cellValue, cellRef, worksheet, columnHeader) => {
    if (typeof cellValue !== 'number' || cellValue < 1 || cellValue > 100000) {
        return false;
    }

    // Check if the column header suggests this is a date column
    if (columnHeader !== null && columnHeader !== undefined && columnHeader !== '') {
        // Safely convert to string and check
        const headerStr = String(columnHeader).trim();
        if (headerStr) {
            const headerLower = headerStr.toLowerCase();
            if (headerLower.includes('date') || headerLower.includes('received') ||
                headerLower.includes('created') || headerLower.includes('updated') ||
                headerLower.includes('time') || headerLower.includes('when')) {
                return true;
            }
        }
    }

    // Check if the cell has a date format
    const cell = worksheet[cellRef];
    if (cell && cell.z) {
        // Check for common date format patterns
        const format = cell.z.toLowerCase();
        return format.includes('m/d/y') || format.includes('d/m/y') ||
            format.includes('yyyy') || format.includes('mm/dd') ||
            format.includes('dd/mm') || format.includes('m-d-y') ||
            format.includes('d-m-y');
    }

    // Fallback: check if it's a reasonable date serial number
    return cellValue > 1 && cellValue < 100000;
};

// Helper function to convert Excel serial date to formatted date string
const convertExcelDate = (serialDate) => {
    try {
        // Excel epoch starts from January 1, 1900 (with a leap year bug)
        const excelEpoch = new Date(1900, 0, 1);
        const jsDate = new Date(excelEpoch.getTime() + (serialDate - 2) * 24 * 60 * 60 * 1000);

        // Check if the resulting date is valid and reasonable
        if (jsDate instanceof Date && !isNaN(jsDate) &&
            jsDate.getFullYear() >= 1900 && jsDate.getFullYear() <= 2100) {
            // Format as YYYY-MM-DD
            const year = jsDate.getFullYear();
            const month = String(jsDate.getMonth() + 1).padStart(2, '0');
            const day = String(jsDate.getDate()).padStart(2, '0');
            return `${year}-${month}-${day}`;
        }
    } catch (e) {
        // If date parsing fails, return null
    }
    return null;
};

const parseExcelFile = async () => {
    try {
        loading.value = true;
        error.value = null;

        // Fetch the file
        const response = await fetch(props.fileUrl);
        if (!response.ok) {
            throw new Error(`Failed to fetch file: ${response.statusText}`);
        }

        const arrayBuffer = await response.arrayBuffer();

        // Parse with XLSX
        const workbook = XLSX.read(arrayBuffer, { type: 'array' });

        // Get the first worksheet
        const firstSheetName = workbook.SheetNames[0];
        if (!firstSheetName) {
            throw new Error('No worksheets found in the Excel file');
        }

        const worksheet = workbook.Sheets[firstSheetName];

        // Convert to JSON with both raw and formatted values
        const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1, raw: true });

        if (!jsonData || jsonData.length === 0) {
            parsedData.value = [];
            headers.value = [];
            return;
        }

        // Filter out completely empty rows
        const filteredData = jsonData.filter(row => {
            return row && Array.isArray(row) && row.some(cell =>
                cell !== null && cell !== undefined && cell !== ''
            );
        });

        if (filteredData.length === 0) {
            parsedData.value = [];
            headers.value = [];
            return;
        }

        // First row as headers
        const headerRow = filteredData[0] || [];
        headers.value = headerRow.map((header, index) => {
            // Safely handle header values that might be null, undefined, or non-string
            let headerTitle;
            if (header === null || header === undefined || header === '') {
                headerTitle = `Column ${index + 1}`;
            } else {
                // Convert to string and trim any whitespace
                headerTitle = String(header).trim() || `Column ${index + 1}`;
            }

            // Determine column width based on header content and data type - made wider for better readability
            let width = 'auto';
            if (headerTitle && typeof headerTitle === 'string') {
                const headerLower = headerTitle.toLowerCase();

                // Short columns for IDs, codes, numbers - increased from 150px
                if (headerLower.includes('id') || headerLower.includes('code') ||
                    headerLower.includes('num') || headerLower.includes('ref') ||
                    headerLower.includes('series') || headerLower.includes('image')) {
                    width = '200px';
                }
                // Medium columns for dates - increased from 150px
                else if (headerLower.includes('date') || headerLower.includes('received') ||
                    headerLower.includes('created') || headerLower.includes('time')) {
                    width = '200px';
                }
                // Medium columns for names, status - increased from 180px
                else if (headerLower.includes('name') || headerLower.includes('status') ||
                    headerLower.includes('by') || headerLower.includes('signatory') ||
                    headerLower.includes('origin') || headerLower.includes('feedback')) {
                    width = '250px';
                }
                // Wider columns for longer text content - increased from 250px
                else if (headerLower.includes('subject') || headerLower.includes('matter') ||
                    headerLower.includes('description') || headerLower.includes('content') ||
                    headerLower.includes('document')) {
                    width = '350px';
                }
                // Default medium width for other columns - increased from 150px
                else {
                    width = '200px';
                }
            }

            return {
                title: headerTitle,
                key: `col_${index}`,
                sortable: true,
                align: 'start',
                width: width
            };
        });

        // Rest as data
        const dataRows = filteredData.slice(1);
        parsedData.value = dataRows.map((row, rowIndex) => {
            const rowData = { id: rowIndex };
            headerRow.forEach((_, colIndex) => {
                let cellValue = row[colIndex];

                // Handle Excel date conversion
                if (cellValue !== undefined && cellValue !== null && cellValue !== '') {
                    try {
                        // Generate cell reference for format checking
                        const cellRef = XLSX.utils.encode_cell({ r: rowIndex + 1, c: colIndex });

                        // Safely get the column header
                        const columnHeader = headerRow[colIndex];

                        // Check if this should be treated as a date
                        if (isExcelDate(cellValue, cellRef, worksheet, columnHeader)) {
                            const convertedDate = convertExcelDate(cellValue);
                            if (convertedDate) {
                                cellValue = convertedDate;
                            }
                        }
                    } catch (dateConversionError) {
                        // If date conversion fails, just use the original value
                        console.warn('Date conversion failed for cell:', cellValue, dateConversionError);
                    }
                }

                // Ensure we always have a string value, handle null/undefined safely
                rowData[`col_${colIndex}`] = cellValue === null || cellValue === undefined ? '' : String(cellValue);
            });
            return rowData;
        });

        // Calculate dynamic pagination options based on data size
        calculatePaginationOptions(dataRows.length);

    } catch (err) {
        console.error('Error parsing Excel file:', err);

        // Provide more specific error messages
        let errorMessage = 'Failed to parse Excel file';
        if (err.message) {
            if (err.message.includes('trim')) {
                errorMessage = 'Error processing Excel file data. The file may contain invalid or corrupted data.';
            } else if (err.message.includes('fetch')) {
                errorMessage = 'Failed to load the Excel file. Please try again.';
            } else {
                errorMessage = `Excel parsing error: ${err.message}`;
            }
        }

        error.value = errorMessage;
    } finally {
        loading.value = false;
    }
};

const handleTableOptions = (options) => {
    const newItemsPerPage = options.itemsPerPage || 10;

    // Check if user selected "All" for large datasets
    if (newItemsPerPage === -1 && totalRows.value > 1000) {
        handleShowAll();
    } else {
        itemsPerPage.value = newItemsPerPage;
    }
};

// Initialize
onMounted(() => {
    parseExcelFile();
});
</script>

<style scoped>
/* Horizontal scrollable container */
.table-container {
    overflow-x: auto;
    overflow-y: visible;
    width: 100%;
}

:deep(.v-data-table-header th) {
    font-weight: bold;
    color: rgba(0, 0, 0, 0.87) !important;
    background-color: white !important;
}

:deep(.v-data-table .v-data-table__td) {
    color: rgba(0, 0, 0, 0.87) !important;
    padding-top: 12px !important;
    padding-bottom: 12px !important;
    white-space: normal;
    word-wrap: break-word;
    line-height: 1.4;
    vertical-align: top;
}

:deep(.v-data-table__th) {
    background-color: white !important;
}

/* Enable horizontal scrolling and wider columns */
:deep(.v-data-table) {
    table-layout: auto;
    min-width: 100%;
    width: max-content;
}

:deep(.v-data-table .v-data-table__td) {
    word-break: break-word;
    hyphens: auto;
    white-space: normal;
}

/* Ensure table headers and cells maintain their width */
:deep(.v-data-table__th) {
    white-space: nowrap;
    min-width: fit-content;
}

/* Smooth scrolling */
.table-container {
    scroll-behavior: smooth;
}

/* Custom scrollbar styling for better UX */
.table-container::-webkit-scrollbar {
    height: 8px;
}

.table-container::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.table-container::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

.table-container::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}
</style>
